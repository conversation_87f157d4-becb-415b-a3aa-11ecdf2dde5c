main:
  push:
    - runner:
        tags: cnb:arch:amd64
      services:
        - docker
      env:
        IMAGE_TAG: ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}:${CNB_BRANCH}-linux-amd64
      stages:
        - name: docker login
          script: docker login -u ${CNB_TOKEN_USER_NAME} -p "${CNB_TOKEN}" ${CNB_DOCKER_REGISTRY}
        - name: docker build
          script: docker build -t ${IMAGE_TAG} .
        - name: docker push
          script: docker push ${IMAGE_TAG}
        - name: resolve
          type: cnb:resolve
          options:
            key: build-amd64

    - runner:
        tags: cnb:arch:arm64:v8
      services:
        - docker
      env:
        IMAGE_TAG: ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}:${CNB_BRANCH}-linux-arm64
      stages:
        - name: docker login
          script: docker login -u ${CNB_TOKEN_USER_NAME} -p "${CNB_TOKEN}" ${CNB_DOCKER_REGISTRY}
        - name: docker build
          script: docker build -t ${IMAGE_TAG} .
        - name: docker push
          script: docker push ${IMAGE_TAG}
        - name: resolve
          type: cnb:resolve
          options:
            key: build-arm64

    - services:
        - docker
      imports:
        - https://cnb.cool/Cp0204/env/-/blob/main/env.yml
      env:
        IMAGE_TAG: ${CNB_REPO_SLUG_LOWERCASE}:${CNB_BRANCH}
        CNB_IMAGE_TAG: ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}:${CNB_BRANCH}
      stages:
        - name: await the amd64
          type: cnb:await
          options:
            key: build-amd64
        - name: await the arm64
          type: cnb:await
          options:
            key: build-arm64

        # 推送到 CNB
        - name: manifest to cnb
          image: cnbcool/manifest
          settings:
            target: ${CNB_IMAGE_TAG}
            template: ${CNB_IMAGE_TAG}-OS-ARCH
            platforms:
              - linux/amd64
              - linux/arm64

        # 推送到 Docker Hub
        - name: manifest to dockerhub
          image: cnbcool/manifest
          settings:
            username: ${DOCKERHUB_USERNAME}
            password: ${DOCKERHUB_TOKEN}
            # 合并后生成的多系统架构镜像
            target:
              - ${IMAGE_TAG}
            # 模板，用于生成多系统架构镜像名称，template 和 platforms 结合，得到用来生成多系统架构的原始镜像名称。
            # 该例子中，原始镜像名称为：foo/bar:v1.0.0-linux-amd64 和 foo/bar:v1.0.0-linux-arm64，这两个镜像必须已经存在
            template: ${CNB_IMAGE_TAG}-OS-ARCH
            # 多系统架构
            platforms:
              - linux/amd64
              - linux/arm64
            # 是否跳过 TLS 证书验证，默认 false
            skipVerify: false
            # 忽略缺失的源镜像，即如果有源镜像缺失不报错，默认false
            ignoreMissing: false

        # 移除多系统架构镜像
        - name: remove tag
          type: artifact:remove-tag
          options:
            name: ${CNB_REPO_NAME}
            tags:
              - ${CNB_BRANCH}-linux-amd64
              - ${CNB_BRANCH}-linux-arm64
            type: docker
