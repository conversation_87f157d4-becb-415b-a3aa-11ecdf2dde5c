import os
import re
import shutil
import traceback
from urllib.parse import quote
from typing import Dict, Any
from drivers._base import BaseDriver
from core.log_manager import LogManager


class StrmGenerator:
    def __init__(
        self,
        storage: BaseDriver,
        task_config: Dict[str, Any],
        strm_config: Dict[str, Any],
    ):
        """初始化 STRM 生成器

        Args:
            storage: 存储实例
            task_config: 任务配置
            strm_config: STRM 配置
        """
        self.storage = storage
        self.task_config = task_config
        self.media_ext = strm_config["media_ext"]
        self.save_dir = f"{strm_config['save_dir']}/{task_config['name']}"
        self.url_encode = strm_config["url_encode"]
        self.media_size = strm_config["media_size"] * 1024 * 1024  # 转换为字节
        self.copy_ext = strm_config["copy_ext"]
        self.smartstrm_base = strm_config["smartstrm_base"]
        self.logger = LogManager.get_task_logger(task_config["name"])
        self.incremental = task_config.get("incremental", True)  # 默认开启增量同步
        self.dir_time_check = task_config.get("dir_time_check", True)  # 目录时间检查
        self.remote_files = []
        self.local_dirs_skip = []

    def _is_media_file(self, filename: str) -> str:
        """判断是否为媒体文件"""
        for ext in self.media_ext:
            if filename.lower().endswith(f".{ext}"):
                return ext
        return None

    def _is_copy_file(self, filename: str) -> bool:
        """判断是否需要直接复制"""
        return any(filename.lower().endswith(f".{ext}") for ext in self.copy_ext)

    def _file_size_check(self, file_info: Dict[str, Any]) -> bool:
        """判断文件大小是否符合条件"""
        if self.media_size <= 0:
            return True
        return file_info.get("size", 0) >= self.media_size

    def _join_normpath(self, path1: str, path2: str = "") -> str:
        """规范路径，并将路径分隔符统一为 /"""
        return os.path.normpath(os.path.join(path1, path2)).replace("\\", "/")

    def _get_local_path(self, path: str) -> str:
        """获取 本地相对 路径"""
        # /drive/quark/video/movie/怪物 (2023)/Kaibutsu.2023.mkv
        relative_path = os.path.relpath(path, self.task_config["storage_path"])
        # /怪物 (2023)/Kaibutsu.2023.mkv
        local_path = self._join_normpath(self.save_dir, relative_path)
        # /strm/movie/怪物 (2023)/Kaibutsu.mkv
        return local_path

    def _get_strm_content(self, media_path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取 STRM 文件内容"""
        url = self.storage.get_strm_url(media_path, file_info)
        url = url.replace("SMARTSTRM_BASE", self.smartstrm_base)
        if self.url_encode and url.lower().startswith("http"):
            return quote(url, safe="/:@&?=%")
        else:
            return url

    def _download(self, path: str, local_path: str, file_info: Dict[str, Any] = {}):
        """下载文件"""
        try:
            file_data = self.storage.get_file_data(path, file_info)
            if file_data:
                # 保存文件
                with open(local_path, "wb") as f:
                    f.write(file_data)
                # 同步文件时间
                self._sync_file_times(local_path, file_info)
            else:
                self.logger.error(f"获取文件数据失败: {path}")
        except Exception as e:
            self.logger.error(f"下载文件失败: {e}")
            traceback.print_exc()

    def _sync_file_times(self, file_path: str, file_info: Dict[str, Any]):
        """同步文件时间

        Args:
            strm_path: STRM 文件路径
            file_info: 文件信息，包含 created 和 modified 时间戳
        """
        try:
            # 获取时间戳
            if created := file_info.get("created"):
                os.utime(file_path, (created, created))
            if modified := file_info.get("modified"):
                os.utime(file_path, (modified, modified))
        except Exception as e:
            self.logger.error(f"同步文件时间失败: {e}")
            traceback.print_exc()

    def _should_scan_directory(self, local_dir: str, file_info: Dict[str, Any]) -> bool:
        """检查是否需要扫描目录

        Args:
            local_dir: 本地目录路径
            remote_file_info: 远程文件信息，包含修改时间

        Returns:
            bool: 是否需要扫描目录
        """
        # 如果未开启目录时间检查，总是扫描
        if not self.dir_time_check:
            return True
        # 如果本地目录不存在，需要扫描
        if not os.path.exists(local_dir):
            return True
        try:
            # 远程目录修改时间
            remote_modified = file_info.get("modified")
            if not remote_modified:
                return True
            # 本地目录修改时间
            local_modified = os.stat(local_dir).st_mtime
            # 比较时间
            is_modified = remote_modified > local_modified
            if is_modified:
                return True
            else:
                return False
        except Exception as e:
            self.logger.error(f"检查目录时间失败: {e}")
            return True

    def handle_path(self, path: str) -> bool:
        """执行指定路径的 STRM 生成和清理"""
        self.logger.info("")
        self.logger.info("=" * 50)
        self.logger.info(f"开始任务: {self.task_config['name']}")
        self.logger.info(f"任务路径: {path}")
        self.logger.info("=" * 50)
        self.remote_files = []
        self.local_dirs_skip = []
        try:
            self.logger.info(f"> 开始生成")
            gen_count = self._generate_strm(path)
            self.logger.debug(f"已跳过目录:  {self.local_dirs_skip}")
            self.logger.info(f"> 开始清理")
            clean_count = self._clean_strm_files(path)
            self.logger.info(
                f"【生成文件完成】 生成 {gen_count['gen_strm']} 个，复制 {gen_count['copy_file']} 个，跳过 {gen_count['skip_file']} 个，跳过目录 {gen_count['skip_dir']} 个"
            )
            self.logger.info(
                f"【清理文件完成】 清理目录 {clean_count['delete_dir']} 个，清理文件 {clean_count['delete_file']} 个，跳过目录 {clean_count['skip_dir']} 个"
            )
            self.logger.info("任务完成")
        except Exception as e:
            self.logger.error(f"执行指定路径的 STRM 生成和清理失败: {e}")
            traceback.print_exc()

    def _generate_strm(self, path: str):
        """生成指定路径下的 STRM 文件

        Args:
            path: 要处理的路径
        """
        count = {
            "gen_strm": 0,
            "copy_file": 0,
            "skip_file": 0,
            "skip_dir": 0,
        }

        # 获取文件列
        self.logger.info(f"读取目录: {path}")
        result = self.storage.list_files(path)
        # 记录应跳过的读取失败的目录
        if not result["success"]:
            self.logger.error(f"读取目录失败: {path} {result['message']}")
            local_dir = self._get_local_path(path)
            count["skip_dir"] += 1
            self.local_dirs_skip.append(local_dir)
            return count
        files = result["data"]
        # self.logger.debug(f" 文件列表: {files}")
        # 确保保存目录存在
        save_dir = (
            f"{self.save_dir}{path.replace(self.task_config['storage_path'], '')}"
        )
        os.makedirs(save_dir, exist_ok=True)

        for file in files:
            file_path = self._join_normpath(path, file["name"])
            self.remote_files.append(file_path)
            if file["isdir"]:
                local_dir = self._get_local_path(file_path)

                # 检查是否需要扫描子目录
                if self._should_scan_directory(local_dir, file):
                    # 创建对应的本地目录
                    os.makedirs(local_dir, exist_ok=True)
                    # 递归处理子目录，传入 remote_files 集合
                    sub_count = self._generate_strm(file_path)
                    # 合并子目录的计数
                    count = {k: count[k] + sub_count[k] for k in sub_count}
                    # 同步目录时间
                    self._sync_file_times(local_dir, file)
                else:
                    count["skip_dir"] += 1
                    self.local_dirs_skip.append(local_dir)

            elif ext := self._is_media_file(file["name"]):
                if self._file_size_check(file):
                    # STRM 文件路径
                    # 文件名 XXXX.(ext).strm 有意而为，保留扩展名，同时避免被 Emby 识别为电影名
                    strm_path = f"{os.path.splitext(self._get_local_path(file_path))[0]}.({ext}).strm"
                    strm_path = self._join_normpath(strm_path)
                    if not self.incremental or not os.path.exists(strm_path):
                        self.logger.info(f"生成 STRM: {strm_path}")
                        count["gen_strm"] += 1
                        # 写入 STRM 文件
                        with open(strm_path, "w", encoding="utf-8") as f:
                            f.write(self._get_strm_content(file_path, file))
                        # 同步文件时间
                        self._sync_file_times(strm_path, file)
                    else:
                        self.logger.debug(f"跳过已存在: {strm_path}")
                        count["skip_file"] += 1
                else:
                    self.logger.debug(f"跳过小于阈值: {file_path}")
                    count["skip_file"] += 1

            elif self._is_copy_file(file["name"]):
                # 直接复制文件
                local_path = self._get_local_path(file_path)
                if not self.incremental or not os.path.exists(local_path):
                    self.logger.info(f"复制文件: {local_path}")
                    count["copy_file"] += 1
                    self._download(file_path, local_path, file)
                else:
                    self.logger.info(f"跳过已存在: {local_path}")
                    count["skip_file"] += 1
        return count

    def _clean_strm_files(self, path: str):
        """清理不存在的 STRM 文件"""
        self.logger.info(f"清理目录: {path}")
        self.logger.debug(f"远程文件集合: {self.remote_files}")
        self.logger.debug(f"跳过目录集合: {self.local_dirs_skip}")
        count = {
            "delete_dir": 0,
            "delete_file": 0,
            "skip_dir": 0,
        }
        # 遍历 STRM 文件目录
        save_dir = (
            f"{self.save_dir}{path.replace(self.task_config['storage_path'], '')}"
        )
        for root, dirs, files in os.walk(save_dir):

            # 跳过因时间未更新而未索引的目录
            if self._join_normpath(root) in self.local_dirs_skip:
                del dirs[:]
                count["skip_dir"] += 1
                continue

            for dir in dirs:
                dir_path = self._join_normpath(root, dir)
                rel_path = os.path.relpath(dir_path, self.save_dir)
                remote_path = self._join_normpath(
                    self.task_config["storage_path"], rel_path
                )
                if remote_path not in self.remote_files:
                    self.logger.info(f"远程目录不存在: {remote_path}")
                    self.logger.info(f"删除本地目录: {dir_path}")
                    shutil.rmtree(dir_path)
                    count["delete_dir"] += 1

            for filename in files:

                if filename.endswith(".strm"):
                    # 还原 XXXX.(ext).strm 为 XXXX.ext
                    filename = re.sub(r"\.\((\w+)\)\.strm$", r".\1", filename)
                elif self._is_copy_file(filename):
                    # 是俺复制的文件
                    pass
                else:
                    continue

                # 获取完整路径
                # /strm/movies + movie.ext -> /strm/movies/movie.ext
                local_path = self._join_normpath(root, filename)
                # 还原相对路径
                # /strm/movies/movie.ext -> /movies/movie.ext
                rel_path = os.path.relpath(local_path, self.save_dir)
                # 还原远程路径
                # /remore + /movies/movie.ext -> /remore/movies/movie.ext
                remote_path = self._join_normpath(
                    self.task_config["storage_path"], rel_path
                )

                # 如果远程文件不存在，删除本地文件
                if remote_path not in self.remote_files:
                    self.logger.info(f"远程文件不存在: {remote_path}")
                    self.logger.info(f"删除本地文件: {local_path}")
                    os.remove(local_path)
                    count["delete_file"] += 1
        return count
