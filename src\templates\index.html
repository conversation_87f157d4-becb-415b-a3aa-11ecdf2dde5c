<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartStrm</title>
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/bootstrap-icons.min.css" rel="stylesheet">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    <link rel="icon" type="image/png" sizes="512x512" href="/static/img/icon.png">
    <style>
        [v-cloak] {
            display: none;
        }

        :root {
            --sidebar-width: 280px;
            --header-height: 60px;
            --content-max-width: 1200px;
            --primary-color: #0d6efd;
            --danger-color: #dc3545;
            --border-color: #eee;
            --text-color: #495057;
            --bg-color: #f8f9fa;
        }

        body {
            min-height: 100vh;
            background-color: var(--bg-color);
        }

        /* 顶部导航栏 */
        .top-nav {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--header-height);
            background: #fff;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
            z-index: 1000;
            padding: 0 1rem;
        }

        .top-nav-brand {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .top-nav-toggle {
            border: none;
            background: none;
            padding: 0.5rem;
            color: var(--text-color);
            font-size: 1.5rem;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            width: var(--sidebar-width);
            background: #fff !important;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            height: var(--header-height);
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }

        .sidebar-brand {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .sidebar-brand img {
            width: 24px;
            height: 24px;
        }

        .nav-link {
            padding: 0.75rem 1.5rem;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            background-color: var(--bg-color);
            color: var(--primary-color);
        }

        .nav-link.active {
            background-color: var(--bg-color);
            color: var(--primary-color);
            font-weight: 500;
        }

        .nav-link i {
            font-size: 1.1rem;
        }

        /* 主内容区样式 */
        .main-content {
            margin-left: var(--sidebar-width);
            padding: 2rem;
            min-height: 100vh;
            background-color: var(--bg-color);
            transition: margin 0.3s ease;
        }

        .content-wrapper {
            max-width: var(--content-max-width);
            margin: 0 auto;
        }

        /* 卡片样式 */
        .card {
            border: none;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
            border-radius: 10px;
            margin-bottom: 1.5rem;
            background: #fff;
        }

        .card-header {
            background: #fff;
            border-bottom: 0px solid var(--border-color);
            padding: 1rem 1.5rem;
            border-radius: 10px 10px 0 0 !important;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* 表格样式 */
        .table {
            margin-bottom: 0;
        }

        .table th {
            font-weight: 600;
            color: var(--text-color);
            border-bottom-width: 1px;
            white-space: nowrap;
        }

        .table td {
            vertical-align: middle;
            color: var(--text-color);
            min-width: 100px;
            max-width: 500px;
        }

        /* 按钮样式 */
        .btn {
            font-weight: 500;
            border-radius: 10px;
        }

        .btn-group-sm {
            display: inline-flex;
            gap: 0.25rem;
        }

        /* 表单样式 */
        .form-control {
            border-radius: 6px;
            border: 1px solid var(--border-color);
            padding: 0.5rem 0.75rem;
        }

        .form-control:focus {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        .form-label {
            font-weight: 500;
            color: var(--text-color);
            margin-bottom: 0.5rem;
        }

        /* 模态框样式 */
        .modal-content {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 1.5rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            border-top: 1px solid var(--border-color);
            padding: 1rem 1.5rem;
        }

        .task-log {
            max-height: 500px;
            overflow-y: auto;
            font-size: 12px;
            font-family: monospace;
            color: #e3e3e3;
            margin-bottom: 0;
        }

        .context-menu {
            position: fixed;
            z-index: 1000;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
            min-width: 150px;
        }

        .cursor-pointer {
            cursor: pointer;
        }

        .toast {
            --bs-toast-bg: rgba(var(--bs-body-bg-rgb), 0.95);
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: var(--header-height) 1rem 1rem;
                margin-top: 1rem;
            }

            .content-wrapper {
                padding: 0;
            }

            .card {
                margin-bottom: 1rem;
            }

            .table-responsive {
                margin: 0 -1.5rem;
                padding: 0 1.5rem;
                width: calc(100% + 3rem);
            }
        }

        /* 许可信息样式 */
        .license-info {
            font-size: 0.8rem;
            text-align: center;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
        }
    </style>
</head>

<body>
    <div id="app" v-cloak>
        <!-- Toast 组件 -->
        <div class="toast-container position-fixed top-0 start-50 translate-middle-x p-3">
            <div id="toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <span class="icon p-2 border rounded me-2"></span>
                    <strong class="me-auto">提示</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body" style="white-space: pre-line"></div>
            </div>
        </div>

        <!-- 顶部导航栏 -->
        <div class="d-block d-md-none">
            <nav class="top-nav  d-flex justify-content-between align-items-center">
                <button class="top-nav-toggle" data-bs-toggle="offcanvas" data-bs-target="#sidebar">
                    <i class="bi bi-list"></i>
                </button>
                <div class="navbar-brand p-0 me-0 me-lg-2">
                    <img src="/static/img/icon.svg" style="width: 24px; height: 24px;">
                </div>
            </nav>
        </div>


        <!-- 侧边栏 -->
        <sidebar class="offcanvas-md offcanvas-start sidebar" id="sidebar">
            <div class="sidebar-header d-flex justify-content-between align-items-center">
                <div class="sidebar-brand d-flex align-items-center">
                    <img src="/static/img/icon.svg"> SmartStrm
                </div>
                <div class="d-block d-md-none">
                    <button type="button" class="btn-close" data-bs-toggle="offcanvas" data-bs-target="#sidebar"></button>
                </div>
            </div>
            <div class="nav flex-column">
                <span class="nav-link cursor-pointer" :class="{ active: activeTab === 'tasks' }" @click="switchTab('tasks')">
                    <i class="bi bi-list-task"></i> 任务管理
                </span>
                <span class="nav-link cursor-pointer" :class="{ active: activeTab === 'storages' }" @click="switchTab('storages')">
                    <i class="bi bi-hdd-network"></i> 存储管理
                </span>
                <span class="nav-link cursor-pointer" :class="{ active: activeTab === 'browser' }" @click="switchTab('browser')">
                    <i class="bi bi-folder2-open"></i> 存储浏览
                </span>
                <span class="nav-link cursor-pointer" :class="{ active: activeTab === 'settings' }" @click="switchTab('settings')">
                    <i class="bi bi-gear"></i> 系统设置
                </span>
            </div>
            <div class="nav-bottom position-absolute bottom-0 w-100 p-3">
                <!-- 许可信息 -->
                <div class="license-info mb-3 p-2 rounded" :class="license.valid ? 'bg-warning-subtle': 'bg-secondary-subtle'">
                    <div v-if="license.email" :class="license.valid ? 'text-warning-emphasis' : 'text-secondary-emphasis'">
                        <div>
                            <span class="me-1" v-html="license.valid ? '👑' : '👤'"></span>{{ license.email }}
                        </div>
                        <div><span class="text-capitalize">{{license.plan}}</span> / <a class="text-decoration-none text-reset" target="_blank" :href="`${license_buy_url}?remark=${license.email}`" title="续订">{{ license.expires_str }}</a> </div>
                    </div>
                    <div v-else class="text-secondary-emphasis">
                        <a class="text-decoration-none text-reset" target="_blank" :href="license_buy_url" title="获取许可">
                            🤪 免费版
                        </a>
                    </div>
                </div>
                <!-- 升级提示 -->
                <div class="text-center mb-4">
                    <p><a class="text-decoration-none text-muted" target="_blank" href="https://github.com/Cp0204/SmartStrm"><i class="bi bi-book-half me-2"></i>SmartStrm</a></p>
                    <p><span class="text-muted" v-html="versionTips"></span></p>
                </div>
                <button class="btn btn-outline-danger w-100" @click="logout">
                    <i class="bi bi-box-arrow-right me-2"></i>退出
                </button>
            </div>
        </sidebar>

        <!-- 主内容区 -->
        <main class="main-content">
            <div class="content-wrapper">
                <!-- 任务管理 -->
                <div v-if="activeTab === 'tasks'" class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">任务管理</h5>
                        <div>
                            <button class="btn btn-primary me-2" @click="runAllTasks">
                                <i class="bi bi-play-fill"></i>运行所有
                            </button>
                            <button class="btn btn-primary" @click="showTaskModal()">
                                <i class="bi bi-plus"></i>添加任务
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>任务名</th>
                                        <th>存储</th>
                                        <th>路径</th>
                                        <th>定时</th>
                                        <th>下次运行</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="text-center" v-if="!storages.length">
                                        <td colspan="6">你还没有存储，<a class="text-decoration-none cursor-pointer" @click="switchTab('storages');showStorageModal()"><i class="bi bi-hdd-network"></i> 去添加</a></td>
                                    </tr>
                                    <tr v-for="(task, name) in tasks" :key="name">
                                        <td>{{ name }}</td>
                                        <td>{{ task.config.storage }}</td>
                                        <td class="cursor-pointer" @click="browserStorage(task.config.storage, task.config.storage_path)">{{ task.config.storage_path }}</td>
                                        <td>{{ task.config.crontab }}</td>
                                        <td>{{ task.next_run || '未设置' }}</td>
                                        <td>
                                            <div class="btn-group-sm">
                                                <button class="btn btn-outline-primary" @click="runTask(name)" title="运行">
                                                    <i class="bi bi-play-fill"></i>
                                                </button>
                                                <button class="btn btn-outline-secondary" @click="showTaskModal(task)" title="编辑">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-outline-info" @click="showTaskLog(name)" title="查看日志">
                                                    <i class="bi bi-journal-text"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" @click="deleteTask(name)" title="删除">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 存储管理 -->
                <div v-if="activeTab === 'storages'" class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">存储管理</h5>
                        <button class="btn btn-primary" @click="showStorageModal()">
                            <i class="bi bi-plus"></i>添加存储
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>存储名</th>
                                        <th>驱动</th>
                                        <th>地址</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="storage in storages" :key="storage.name">
                                        <td>{{ storage.name }}</td>
                                        <td>{{ storage.driver }}</td>
                                        <td>
                                            <template v-if="storage.user_info">
                                                <small>
                                                    <div class="d-flex flex-column flex-md-row align-items-md-center">
                                                        <div class="d-flex align-items-center">
                                                            <img :src="storage.user_info.user_face" style="width: 16px; height: 16px;" crossorigin="anonymous" class="rounded-circle me-2">
                                                            <span>{{ storage.user_info.user_name }}</span>
                                                            <a v-if="storage.user_info.vip_url" :href="storage.user_info.vip_url" target="_blank" class="d-flex text-decoration-none">
                                                                <span class="ms-2 badge bg-secondary rounded-pill">{{ storage.user_info.vip_level }}</span>
                                                            </a>
                                                            <span v-else class="ms-2 badge bg-secondary rounded-pill">{{ storage.user_info.vip_level }}</span>
                                                        </div>
                                                        <div class="ms-md-auto text-md-end">
                                                            {{ formatFileSize(storage.user_info.space_info.use,1) }} / {{ formatFileSize(storage.user_info.space_info.total,0) }}
                                                        </div>
                                                    </div>
                                                </small>
                                                <div class="progress" role="progressbar" style="height: 6px; margin-top: 3px;">
                                                    <div class="progress-bar progress-bar-striped progress-bar-animated" :style="{width:(storage.user_info.space_info.use/storage.user_info.space_info.total*100)+'%'}"></div>
                                                </div>
                                            </template>
                                            <template v-else>
                                                {{ storage.hostname || storage.url || storage.root_path }}
                                            </template>
                                        </td>
                                        <td>
                                            <div class="btn-group-sm">
                                                <button class="btn btn-outline-secondary" @click="showStorageModal(storage)" title="编辑">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-outline-success" @click="testStorage(storage.name)" title="测试">
                                                    <i class="bi bi-check"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" @click="deleteStorage(storage.name)" title="删除">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 系统设置 -->
                <div v-if="activeTab === 'settings'" class="card">
                    <div class="card-header">
                        <div class="nav nav-tabs" id="nav-tab" role="tablist">
                            <button class="nav-link active" id="nav-strm-tab" data-bs-toggle="tab" data-bs-target="#nav-strm" type="button" role="tab" aria-controls="nav-strm" aria-selected="true">STRM 设置</button>
                            <button class="nav-link" id="nav-rename-tab" data-bs-toggle="tab" data-bs-target="#nav-rename" type="button" role="tab" aria-controls="nav-rename" aria-selected="false">重命名设置</button>
                            <button class="nav-link position-relative" id="nav-proxy-tab" data-bs-toggle="tab" data-bs-target="#nav-proxy" type="button" role="tab" aria-controls="nav-proxy" aria-selected="false">302代理
                                <span v-if="!license.valid" class="position-absolute top-0 start-100 translate-middle badge rounded-pill text-bg-warning">Pro</span>
                            </button>
                            <button class="nav-link" id="nav-webhook-tab" data-bs-toggle="tab" data-bs-target="#nav-webhook" type="button" role="tab" aria-controls="nav-webhook" aria-selected="false">Webhook</button>
                        </div>
                    </div>
                    <div class="card-body ">
                        <div class="tab-content">
                            <div class="tab-pane show active" id="nav-strm" role="tabpanel" aria-labelledby="nav-strm-tab">
                                <div class="mb-3">
                                    <label class="form-label">媒体文件后缀</label>
                                    <input type="text" class="form-control" v-model="settings.strm.media_ext" placeholder=".mp4,.mkv">
                                    <div class="form-text">多个后缀用逗号分隔，这些文件将生成 STRM</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">媒体文件大小阈值</label>
                                    <div class="input-group">
                                        <span class="input-group-text">≥</span>
                                        <input type="number" class="form-control" v-model="settings.strm.media_size" min="0" step="0.1">
                                        <span class="input-group-text">MB</span>
                                    </div>
                                    <div class="form-text">大于此大小的媒体文件将才会生成 STRM</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">复制到本地的文件后缀</label>
                                    <input type="text" class="form-control" v-model="settings.strm.copy_ext" placeholder=".jpg,.png,.nfo">
                                    <div class="form-text">多个后缀用逗号分隔，这些文件将直接复制(下载)到生成目录</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">生成根目录</label>
                                    <input type="text" class="form-control" v-model="settings.strm.save_dir">
                                    <div class="form-text">容器内部目录，非必要不用修改，任务将生成至 <code>{{settings.strm.save_dir}}/任务名</code></div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" v-model="settings.strm.url_encode" id="urlEncode">
                                        <label class="form-check-label" for="urlEncode">对 STRM 进行 URL 编码</label>
                                    </div>
                                    <div class="form-text">以增强非英文路径的兼容性，你可能会看到像 <code class="text-muted">%E5%A6%99</code> 的路径，并非乱码</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">SmartStrm 地址</label>
                                    <input type="text" class="form-control" v-model="settings.strm.smartstrm_base" placeholder="http://192.168.8.8:8024">
                                    <div class="form-text">供 SmartStrm STRM 服务使用，如果不用代理服务，需被 Emby 访问到</div>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <button type="button" class="btn btn-primary" @click="saveSettings('strm')">保存设置</button>
                                </div>
                            </div>
                            <div class="tab-pane" id="nav-rename" role="tabpanel" aria-labelledby="nav-rename-tab">
                                <div class="mb-3">
                                    <label class="form-label">电影命名规则</label>
                                    <input type="text" class="form-control" v-model="settings.rename.movie">
                                    <div class="form-text">你习惯的命名规则，如：<code>{TITLE}.{YEAR}.{EXT} → 电影名.2025.mp4</code></div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">剧集命名规则</label>
                                    <input type="text" class="form-control" v-model="settings.rename.tv">
                                    <div class="form-text">你习惯的命名规则，如：<code>{TITLE}.S{SXX}E{E}.{EXT} → 剧集名.S01E01.mkv</code></div>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <button type="button" class="btn btn-primary" @click="saveSettings('rename')">保存设置</button>
                                </div>
                            </div>
                            <div class="tab-pane" id="nav-proxy" role="tabpanel" aria-labelledby="nav-proxy-tab">
                                <!-- Pro许可提示 -->
                                <div v-if="!license.valid" class="alert alert-warning mb-3">
                                    <i class="bi bi-shield-exclamation me-2"></i>
                                    <strong>功能限制</strong> - 代理服务器功能需要 Pro 许可
                                </div>

                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">代理服务器</h6>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" :class="settings.proxy.enabled ? 'bg-success' : ''" type="checkbox" role="switch" id="proxyEnabled" v-model="settings.proxy.enabled" @change="saveSettings('proxy')" :disabled="!license.valid">
                                            <label class="form-check-label" for="proxyEnabled">{{ settings.proxy.enabled ? '已启用' : '已禁用' }}</label>
                                        </div>
                                    </div>
                                    <div class="alert" :class="settings.proxy.is_running ? 'alert-success' : 'alert-secondary'">
                                        <i class="bi me-2" :class="settings.proxy.is_running ? 'bi-check-circle-fill' : 'bi-circle-fill'"></i>
                                        <a v-if="settings.proxy.is_running" :href="settings.proxy.url" target='_blank' class="text-decoration-none text-reset">代理服务器正在运行<span class="ms-2" v-if="settings.proxy.title">({{ settings.proxy.title }})</span></a>
                                        <span v-else>代理服务器已停止</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">服务端口</label>
                                    <input type="number" class="form-control" v-model="settings.proxy.port" placeholder="8097" :disabled="settings.proxy.is_running || !license.valid">
                                    <div class="form-text">以此端口访问代理服务器，需把该端口映射到宿主机，或网络模式设为 <code>host</code></div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">媒体服务器地址</label>
                                    <input type="text" class="form-control" v-model="settings.proxy.target" placeholder="http://emby:8096" :disabled="settings.proxy.is_running || !license.valid">
                                    <div class="form-text">代理请求将转发到此地址，可以填写内网 (192.168.x.x) 或 Docker Bridge (172.17.0.x) 地址，需被本容器访问到</div>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <button type="button" class="btn btn-primary" @click="saveSettings('proxy')" :disabled="settings.proxy.is_running || !license.valid">保存设置</button>
                                </div>
                            </div>
                            <div class="tab-pane" id="nav-webhook" role="tabpanel" aria-labelledby="nav-webhook-tab">
                                <div class="mb-3">
                                    <label class="form-label">Webhook 地址</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" v-model="settings.webhook_url" readonly>
                                        <button class="btn btn-outline-dark" type="button" @click="copyText(settings.webhook_url)" title="复制"><i class="bi bi-clipboard"></i></button>
                                    </div>
                                    <div class="form-text">以上链接含 token 信息，用于验证身份，请勿泄露</div>
                                </div>
                                <div class="mb-3 alert alert-light">
                                    <h6 class="alert-heading"><b><i class="bi bi-robot me-2"></i>当前支持</b></h6>
                                    <ul class="mb-0">
                                        <li>Emby 通知测试：点击测试后 Docker 容器日志显示，供测试连通性</li>
                                        <li>Emby 媒体删除事件：同步删除远程存储（网盘）中的文件。注意！当媒体库监控到本地 STRM 文件删除时，也会触发远程删除</li>
                                        <li>运行一个任务：可仅触发任务路径下的子目录，支持自定义STRM参数，精准生成</li>
                                        <li>联动 QAS 触发任务：转存后触发 STRM 生成，可仅触发任务路径下的子目录，精准生成</li>
                                        <li>联动 CloudSaver 触发任务：转存后触发 STRM 生成，可仅触发任务路径下的子目录，精准生成</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文件浏览器 -->
                <div v-if="activeTab === 'browser'" class="card">
                    <div class="card-header">
                        <div class="input-group mb-3">
                            <span class="input-group-text"><i class="bi bi-hdd-network"></i></span>
                            <span class="input-group-text cursor-pointer" data-bs-toggle="dropdown" aria-expanded="false">{{ browser.storage || '选择存储 ' }}<i class="ms-1 bi bi-caret-down-fill"></i></span>
                            <ul class="dropdown-menu">
                                <li v-for="storage in storages" :key="storage.name" :value="storage.name">
                                    <a class="dropdown-item" href="#/browser" @click="browserStorage(storage.name, '/')">{{ storage.name }}</a>
                                </li>
                            </ul>
                            <input type="text" class="form-control" v-model="browser.path" @keyup.enter="browseFiles(browser.path)" @change="browseFiles(browser.path)">
                            <button class="btn btn-outline-dark" type="button" @click="newTaskFromBrowser()" title="新建任务"><i class="bi bi-journal-plus"></i></button>
                            <button class="btn btn-outline-dark" type="button" @click="copyText(browser.path)" title="复制"><i class="bi bi-clipboard"></i></button>
                            <button class="btn btn-outline-dark" type="button" @click="browseFiles(browser.path)" title="刷新"><i class="bi bi-arrow-clockwise"></i></button>
                        </div>
                        <div class="input-group mb-3">
                            <button class="btn btn-sm" type="button" :class="batchRename.enabled ? 'btn-dark' : 'btn-outline-dark'" @click="toggleRenameMode">
                                <i class="bi bi-pencil-square me-2"></i>批量重命名
                            </button>

                        </div>
                        <div v-if="batchRename.enabled" class="alert alert-light rename-controls">
                            <div class="row g-3 align-items-center">
                                <div class="col-auto">
                                    <div class="input-group">
                                        <span class="input-group-text">模式</span>
                                        <select class="form-select" v-model="batchRename.mode" @change="previewRename">
                                            <option value="magic">智能命名</option>
                                            <option value="regex">正则替换</option>
                                            <option value="sequence">顺序命名</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-auto" v-if="batchRename.mode === 'regex'">
                                    <div class="input-group">
                                        <span class="input-group-text">匹配式</span>
                                        <input type="text" class="form-control" v-model="batchRename.config.regex.pattern" placeholder="如：EP(\d+)" @input="previewRename">
                                        <span class="input-group-text">替换为</span>
                                        <input type="text" class="form-control" v-model="batchRename.config.regex.replace" placeholder="如：E$1" @input="previewRename">
                                    </div>
                                </div>
                                <div class="col-auto" v-if="batchRename.mode === 'sequence'">
                                    <div class="input-group">
                                        <span class="input-group-text">前缀</span>
                                        <input type="text" class="form-control" v-model="batchRename.config.sequence.prefix" placeholder="如：S01E" @input="previewRename">
                                        <span class="input-group-text">起编</span>
                                        <input type="number" class="form-control" v-model="batchRename.config.sequence.startNum" min="1" @input="previewRename">
                                        <span class="input-group-text">后缀</span>
                                        <input type="text" class="form-control" v-model="batchRename.config.sequence.suffix" placeholder=".mp4" @input="previewRename">
                                    </div>
                                </div>
                                <div class="col-auto" v-if="batchRename.enabled">
                                    <button class="btn btn-success" @click="applyRename" :disabled="!batchRename.canApply || batchRename.renaming">
                                        <span v-if="batchRename.renaming" class="spinner-border spinner-border-sm me-1" role="status"></span>
                                        <i v-else class="bi bi-check"></i>
                                        {{ batchRename.renaming ? '重命名中...' : '应用修改' }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>名称</th>
                                        <th v-if="batchRename.enabled">新文件名</th>
                                        <th>大小</th>
                                        <th>修改时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-if="browser.path !== '/'">
                                        <td class="cursor-pointer" @click.prevent="browseFiles(getParentPath(browser.path))">
                                            <i class="me-2 bi bi-folder-fill text-warning"></i>..
                                        </td>
                                        <td v-if="batchRename.enabled"></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr v-for="(file, index) in browser.files" :key="file.name" @contextmenu.prevent="showContextMenu($event, file)">
                                        <td class="cursor-pointer" @click.prevent="file.isdir ? browseFiles(browser.path + '/' + file.name) : null">
                                            <div class="d-flex align-items-center">
                                                <i class="me-2" :class="file.isdir ? 'bi bi-folder-fill text-warning' : 'bi bi-file-earmark'"></i>
                                                <template v-if="contextMenu.file && contextMenu.file.name === file.name && contextMenu.isRenaming">
                                                    <div class="input-group input-group-sm" @click.prevent.stop>
                                                        <input type="text" class="form-control" v-model="contextMenu.newName" @keyup.enter="confirmRename" @keyup.esc="cancelRename">
                                                        <button class="btn btn-success" type="button" @click="confirmRename">
                                                            <i class="bi bi-check"></i>
                                                        </button>
                                                    </div>
                                                </template>
                                                <template v-else>
                                                    <span class="text-break" :title="file.name">{{ file.name }}</span>
                                                </template>
                                            </div>
                                        </td>
                                        <td v-if="batchRename.enabled">
                                            <input v-if="!file.isdir" type="text" class="w-100 border-0" :class="file.name != file.new_name ? 'text-success' : 'text-muted'" v-model="file.new_name" :placeholder="file.name">
                                        </td>
                                        <td>{{ formatFileSize(file.size) }}</td>
                                        <td>{{ formatDate(file.modified) }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 右键菜单 -->
                <div class="context-menu" v-show="contextMenu.visible" :style="contextMenu.style">
                    <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action" @click.prevent="startRename">
                            <i class="bi bi-pencil"></i> 重命名
                        </a>
                        <a href="#" class="list-group-item list-group-item-action text-danger" @click.prevent="deleteFile">
                            <i class="bi bi-trash"></i> 删除
                        </a>
                    </div>
                </div>
            </div>
        </main>

        <!-- 存储配置模态框 -->
        <div class="modal fade" id="storageModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ editingStorage.name ? '编辑存储' : '添加存储' }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form @submit.prevent="saveStorage">
                            <div class="mb-3">
                                <label class="form-label">存储名称</label>
                                <input type="text" class="form-control" v-model="editingStorage.name" placeholder="myquark" required>
                                <div class="form-text">自定义，存储名称用于任务引用，请勿重复</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">驱动</label>
                                <select class="form-select" v-model="editingStorage.driver" required>
                                    <option v-for="driver in availableDrivers" :key="driver.type" :value="driver.type">
                                        {{ driver.name }}
                                    </option>
                                </select>
                            </div>
                            <template v-if="editingStorage.driver">
                                <div v-if="driverTips.message" :class="'mb-3 alert alert-' + driverTips.type">
                                    <span v-html="driverTips.message"></span>
                                </div>
                                <template v-for="(field, key) in driverConfig" :key="key">
                                    <div v-if="field.type != 'hidden'" class="mb-3">
                                        <template v-if="field.type === 'boolean'">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" v-model="editingStorage[key]" :id="key">
                                                <label class="form-check-label" :for="key">{{ field.label }}</label>
                                            </div>
                                        </template>
                                        <template v-else>
                                            <label class="form-label">{{ field.label }}</label>
                                            <input :type="field.type" class="form-control" v-model="editingStorage[key]" :placeholder="field.placeholder" :required="field.required">
                                        </template>
                                        <div v-if="field.tip" class="form-text" v-html="field.tip"></div>
                                    </div>
                                </template>
                            </template>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" @click="saveStorage">保存</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务配置模态框 -->
        <div class="modal fade" id="taskModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ editingTask.name ? '编辑任务' : '添加任务' }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form @submit.prevent="saveTask">
                            <div class="alert alert-info">STRM 将保存在：<code>{{ settings.strm.save_dir + '/' + editingTask.name }}</code></div>
                            <div class="mb-3">
                                <label class="form-label">任务名称</label>
                                <input type="text" class="form-control" v-model="editingTask.name" placeholder="tv" required>
                                <div class="form-text">唯一值，修改已有的即为复制新建一个任务</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">使用存储</label>
                                <select class="form-select" v-model="editingTask.storage" required>
                                    <option v-for="storage in storages" :key="storage.name" :value="storage.name">
                                        {{ storage.name }}
                                    </option>
                                </select>
                                <div class="form-text" v-if="!storages.length">你还没有存储，<a class="text-decoration-none cursor-pointer" data-bs-dismiss="modal" @click="switchTab('storages');showStorageModal()"><i class="bi bi-hdd-network"></i> 去添加</a></div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">扫描路径</label>
                                <input type="text" class="form-control" v-model="editingTask.storage_path" placeholder="/video/tv" required>
                                <div class="form-text">存储中媒体文件的路径</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">执行时间</label>
                                <input type="text" class="form-control" v-model="editingTask.crontab" placeholder="0 0 * * *" @dblclick="generateRandomCrontab">
                                <div class="form-text"><a class="text-muted" href="https://tool.lu/crontab/" target="_blank">Crontab</a> 格式，留空则不自动执行；双击填为随机每天半夜时间</div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" v-model="editingTask.incremental" id="incremental">
                                    <label class="form-check-label" for="incremental">增量同步</label>
                                </div>
                                <div class="form-text">开启后只新增不存在的，不覆写已存在的</div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" v-model="editingTask.dir_time_check" id="dirTimeCheck">
                                    <label class="form-check-label" for="dirTimeCheck">目录时间检查</label>
                                </div>
                                <div class="form-text">开启后仅当远程目录修改时间>本地目录时才扫描子目录</div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" @click="saveTask">保存</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务日志模态框 -->
        <div class="modal fade" id="taskLogModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">任务日志 - {{ viewingTaskLog }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <pre class="bg-dark p-2 task-log rounded">{{ taskLog }}</pre>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" @click="refreshTaskLog">刷新</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/vue.global.prod.js"></script>
    <script src="/static/js/axios.min.js"></script>
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    activeTab: 'tasks',
                    storages: [],
                    tasks: {},
                    settings: {
                        strm: {
                            media_ext: [],
                            media_size: 20,
                            copy_ext: [],
                            save_dir: "./strm",
                            url_encode: true,
                        },
                        rename: {
                            movie: "",
                            tv: "",
                        },
                        webhook_url: '',
                        proxy: {
                            enabled: false,
                            port: 8097,
                            target: '',
                            url: '',
                            is_running: false
                        }
                    },
                    license_buy_url: 'https://afdian.com/item/cd39184033a811f0b60b52540025c377',
                    license: {
                        valid: false,
                    },
                    editingStorage: {},
                    editingStorage_default: {
                        name: '',
                        driver: 'openlist',
                    },
                    editingTask: {},
                    editingTask_default: {
                        name: '',
                        storage: '',
                        storage_path: '',
                        crontab: '0 0 * * *',
                        incremental: true,
                        dir_time_check: true
                    },
                    storageModal: null,
                    taskModal: null,
                    taskLogModal: null,
                    versionTips: 'dev',
                    availableDrivers: [],
                    viewingTaskLog: '',
                    taskLog: '',
                    toast: null,
                    browser: {
                        storage: '',
                        path: '/',
                        files: []
                    },
                    contextMenu: {
                        visible: false,
                        style: {
                            left: '0px',
                            top: '0px'
                        },
                        file: null,
                        newName: '',
                        isRenaming: false
                    },
                    batchRename: {
                        enabled: false,
                        mode: 'magic',
                        canApply: false,
                        renaming: false,
                        config: {
                            regex: {
                                pattern: '',
                                replace: ''
                            },
                            sequence: {
                                prefix: '',
                                startNum: 1,
                                suffix: ''
                            }
                        }
                    },
                };
            },
            mounted() {
                // 监听浏览器前进后退按钮
                window.addEventListener('popstate', this.handlePopState);
                this.handlePopState(); // 初始化时根据URL设置当前标签页

                this.storageModal = new bootstrap.Modal(document.getElementById('storageModal'));
                this.taskModal = new bootstrap.Modal(document.getElementById('taskModal'));
                this.taskLogModal = new bootstrap.Modal(document.getElementById('taskLogModal'));
                this.toast = new bootstrap.Toast(document.getElementById('toast'));

                // 加载数据
                this.loadData();

                // 检查更新
                this.checkUpdate();

                // 点击任意地方关闭右键菜单和重命名
                document.addEventListener('click', (event) => {
                    this.handleGlobalClick(event)
                });
            },
            beforeUnmount() {
                window.removeEventListener('resize', this.handleResize);
                document.removeEventListener('click', this.handleGlobalClick);
            },
            methods: {
                // ==============================
                // UI
                // ==============================
                handlePopState() {
                    // 从URL初始化标签页
                    const hash = window.location.hash;
                    if (hash.startsWith('#/')) {
                        const tab = hash.substring(2);
                        if (['tasks', 'storages', 'browser', 'settings'].includes(tab)) {
                            this.activeTab = tab;
                        }
                    }
                },
                switchTab(tab = null) {
                    this.activeTab = tab;
                    window.location.hash = `#/${tab}`;
                    // 使用 Bootstrap 的 Offcanvas 实例来隐藏侧边栏
                    const sidebar = document.getElementById('sidebar');
                    if (sidebar) {
                        const offcanvas = bootstrap.Offcanvas.getInstance(sidebar);
                        if (offcanvas) {
                            offcanvas.hide();
                        }
                    }
                },
                showTaskModal(task = null) {
                    this.editingTask = task ? { ...task.config, name: task.name } : this.editingTask_default;
                    this.taskModal.show();
                },
                showStorageModal(storage = null) {
                    this.editingStorage = storage ? { ...storage } : this.editingStorage_default;
                    this.storageModal.show();
                },
                showToast(message, type = 'info') {
                    const toastEl = document.getElementById('toast');
                    const toastBody = toastEl.querySelector('.toast-body');
                    const toastHeaderIcon = toastEl.querySelector('.toast-header .icon');
                    toastBody.innerHTML = message;
                    toastHeaderIcon.classList.remove('bg-primary', 'bg-secondary', 'bg-success', 'bg-danger', 'bg-warning', 'bg-info', 'bg-dark');
                    toastHeaderIcon.classList.add(`bg-${type}`);
                    this.toast.show();
                },
                generateRandomCrontab() {
                    // 生成半夜0-5点之间的随机时间
                    const hour = Math.floor(Math.random() * 6); // 0-5点
                    const minute = Math.floor(Math.random() * 60); // 0-59分钟
                    this.editingTask.crontab = `${minute} ${hour} * * *`;
                },
                async logout() {
                    await axios.post('/api/logout');
                    window.location.href = '/';
                },
                // ==============================
                // 数据
                // ==============================
                async loadData() {
                    try {
                        const [storagesRes, tasksRes, settingsRes, availableDriversRes, licenseRes] = await Promise.all([
                            axios.get('/api/storages'),
                            axios.get('/api/tasks'),
                            axios.get('/api/settings'),
                            axios.get('/api/drivers/available'),
                            axios.get('/api/license')
                        ]);
                        this.storages = storagesRes.data;
                        this.tasks = tasksRes.data;
                        this.settings = {
                            ...settingsRes.data,
                            strm: {
                                ...settingsRes.data.strm,
                                media_ext: settingsRes.data.strm.media_ext.join(','),
                                copy_ext: settingsRes.data.strm.copy_ext.join(',')
                            }
                        };
                        this.availableDrivers = availableDriversRes.data;
                        this.license = licenseRes.data;
                    } catch (error) {
                        console.error('加载后端数据失败:', error);
                        this.showToast('加载后端数据失败', "danger");
                    }
                },
                async checkUpdate() {
                    try {
                        const [local, remote] = await Promise.all([
                            axios.get('/api/version'),
                            axios.get('https://raw.githubusercontent.com/Cp0204/SmartStrm/main/version.json')
                        ]);
                        this.versionTips = local.data.version;
                        if (local.data.version != remote.data.version) {
                            this.showToast('有新版本可用 ' + remote.data.version, "success");
                            this.versionTips += '<sup><span class="position-absolute rounded-pill badge text-bg-danger">' + remote.data.version + '</span></sup>';
                        }
                    } catch (error) {
                        console.error('检查更新失败:', error);
                    }
                },

                // ==============================
                // 任务
                // ==============================
                async saveTask() {
                    try {
                        if (!this.editingTask.name || !this.editingTask.storage || !this.editingTask.storage_path) {
                            this.showToast('错误：缺少必须参数', "danger");
                            return;
                        };
                        const response = await axios.post(`/api/tasks/${this.editingTask.name}`, this.editingTask);
                        if (response.data.message) {
                            console.log(response.data.message);
                        };
                        this.taskModal.hide();
                        await this.loadData();
                    } catch (error) {
                        console.error('保存任务失败:', error);
                        this.showToast('保存任务失败: ' + (error.response?.data?.message || error.message), "danger");
                    }
                },
                async deleteTask(name) {
                    if (!confirm(`确定要删除任务 ${name} 吗？`)) return;
                    try {
                        await axios.delete(`/api/tasks/${name}`);
                        this.loadData();
                    } catch (error) {
                        console.error('删除任务失败:', error);
                        this.showToast('删除任务失败', "danger");
                    }
                },
                async runTask(name) {
                    try {
                        const response = await axios.post(`/api/tasks/${name}/run`);
                        if (response.data.message) {
                            this.showToast(response.data.message, "success");
                        };
                        this.showTaskLog(name);
                    } catch (error) {
                        console.error('启动任务失败:', error);
                        this.showToast('启动任务失败: ' + (error.response?.data?.message || error.message), "danger");
                    }
                },
                async runAllTasks() {
                    try {
                        const response = await axios.post('/api/run');
                        if (response.data.message) {
                            this.showToast(response.data.message, "success");
                        };
                    } catch (error) {
                        console.error('启动所有任务失败:', error);
                        this.showToast('启动所有任务失败: ' + (error.response?.data?.message || error.message), "danger");
                    }
                },
                async showTaskLog(taskName) {
                    this.viewingTaskLog = taskName;
                    await this.refreshTaskLog();
                    this.taskLogModal.show();
                },
                async refreshTaskLog() {
                    this.taskLog += '\nLoading...';
                    try {
                        const response = await axios.get(`/api/tasks/${this.viewingTaskLog}/log`);
                        this.taskLog = response.data.log ? response.data.log : '日志为空';
                        // 滚动到最底部
                        setTimeout(() => {
                            const logContainer = document.querySelector('.task-log');
                            logContainer.scrollTop = logContainer.scrollHeight;
                        }, 200);
                    } catch (error) {
                        console.error('获取任务日志失败:', error);
                        this.taskLog = '获取日志失败: ' + (error.response?.data?.message || error.message);
                    }
                },
                // ==============================
                // 存储
                // ==============================
                async saveStorage() {
                    if (!this.editingStorage.name || !this.editingStorage.driver) {
                        this.showToast('错误：缺少存储名称或驱动', "danger");
                        return;
                    };
                    try {
                        const response = await axios.post(`/api/storages/${this.editingStorage.name}`, this.editingStorage);
                        if (response.data.message) {
                            console.log(response.data.message);
                        };
                        this.storageModal.hide();
                        this.loadData();
                    } catch (error) {
                        console.error('保存存储失败:', error);
                        this.showToast('保存存储失败', "danger");
                    }
                },
                isStorageInUse(storageName) {
                    return Object.values(this.tasks).some(task => task.config.storage === storageName);
                },
                async deleteStorage(name) {
                    if (this.isStorageInUse(name)) {
                        this.showToast('该存储正在被任务使用，无法删除', "warning");
                        return;
                    };
                    if (!confirm(`确定要删除存储 ${name} 吗？`)) return;
                    try {
                        await axios.delete(`/api/storages/${name}`);
                        this.loadData();
                    } catch (error) {
                        console.error('删除存储失败:', error);
                        this.showToast('删除存储失败', "danger");
                    }
                },
                async testStorage(name) {
                    await axios.post(`/api/storage/${name}/test`).then(response => {
                        if (response.data.success) {
                            let list = response.data.data.map(item => `└${item.isdir ? '📁' : '📄'}${item.name}`).join('\n');
                            this.showToast(response.data.message + `，根目录列表：\n<pre class="text-bg-dark p-2 rounded"><i class="me-1 bi bi-hdd-network"></i>${name}\n${list}</pre>`, "success");
                        } else {
                            this.showToast(response.data.message, "danger");
                        }
                    }).catch(error => {
                        console.error('测试存储失败:', error);
                        this.showToast('测试存储失败: ' + (error.response?.data?.message || error.message), "danger");
                    });
                },
                // ==============================
                // 设置
                // ==============================
                async saveSettings(type) {
                    try {
                        const settings = {};
                        console.log(settings);
                        if (type == 'strm') {
                            settings.strm = {
                                media_ext: this.settings.strm.media_ext.split(',').map(ext => ext.trim()).filter(ext => ext),
                                media_size: parseFloat(this.settings.strm.media_size),
                                copy_ext: this.settings.strm.copy_ext.split(',').map(ext => ext.trim()).filter(ext => ext),
                                save_dir: this.settings.strm.save_dir,
                                url_encode: this.settings.strm.url_encode,
                                smartstrm_base: this.settings.strm.smartstrm_base,
                            };
                        } else if (type == 'rename') {
                            settings.rename = {
                                movie: this.settings.rename.movie,
                                tv: this.settings.rename.tv
                            };
                        } else if (type == 'proxy') {
                            settings.proxy = {
                                enabled: this.settings.proxy.enabled,
                                port: this.settings.proxy.port,
                                target: this.settings.proxy.target
                            };
                        }
                        const response = await axios.post('/api/settings', settings);
                        if (response.data.message) {
                            this.showToast(response.data.message, "success");
                            console.log(response.data.message);
                        };
                        await this.loadData();
                    } catch (error) {
                        console.error('保存设置失败:', error);
                        this.showToast('保存设置失败: ' + (error.response?.data?.message || error.message), "danger");
                    }
                },
                // ==============================
                // 文件浏览
                // ==============================
                copyText(text) {
                    if (!text) {
                        this.showToast(`没有可复制的内容`, "info");
                        return;
                    }
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text);
                    } else {
                        const textarea = document.createElement('textarea');
                        textarea.value = text;
                        textarea.style.position = 'fixed';
                        textarea.style.top = '0';
                        textarea.style.left = '0';
                        textarea.style.opacity = '0';
                        document.body.appendChild(textarea);
                        textarea.select();
                        textarea.setSelectionRange(0, 99999);
                        document.execCommand("copy");
                        document.body.removeChild(textarea);
                    }
                    this.showToast(`<code>${text}</code>\n已复制到剪贴板`, "success");
                },
                browserStorage(storage, path) {
                    this.browser.files = [];
                    this.browser.storage = storage;
                    this.browseFiles(path);
                    window.location.hash = `#/browser`;
                },
                newTaskFromBrowser() {
                    this.editingTask = {
                        name: "",
                        storage: this.browser.storage,
                        storage_path: this.browser.path,
                        crontab: "0 0 * * *",
                        incremental: true,
                        dir_time_check: true
                    }
                    this.taskModal.show();
                },
                async browseFiles(path = '/') {
                    path = path.replace(/\/+/, "/");
                    if (!this.browser.storage) {
                        this.showToast('请先选择存储', "info");
                        this.browser.files = [];
                        return;
                    }
                    try {
                        let params = { path };
                        if (this.batchRename.enabled) {
                            params.rename_mode = this.batchRename.mode
                        }
                        const response = await axios.get(`/api/storage/${this.browser.storage}/browse`, {
                            params: params
                        });
                        if (response.data.success) {
                            this.browser.path = response.data.path;
                            this.browser.files = response.data.files;
                            if (this.batchRename.enabled)
                                this.previewRename();
                        } else {
                            this.showToast(response.data.message, "danger");
                        }
                    } catch (error) {
                        console.error('浏览文件失败:', error);
                        this.showToast('浏览文件失败: ' + (error.response?.data?.message || error.message), "danger");
                    }
                },
                getParentPath(path) {
                    const parts = path.split('/').filter(Boolean);
                    parts.pop();
                    return '/' + parts.join('/');
                },
                formatFileSize(size, point = 2) {
                    if (size === undefined || size === null || size === "") return '';
                    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
                    let value = size;
                    let unitIndex = 0;
                    while (value >= 1024 && unitIndex < units.length - 1) {
                        value /= 1024;
                        unitIndex++;
                    }
                    return `${value.toFixed(point)} ${units[unitIndex]}`;
                },
                formatDate(timestamp) {
                    if (!timestamp) return '';
                    return new Date(timestamp * 1000).toLocaleString();
                },
                // ==============================
                // 右键菜单
                // ==============================
                showContextMenu(event, file) {
                    this.contextMenu.visible = true;
                    this.contextMenu.style.left = event.pageX + 'px';
                    this.contextMenu.style.top = event.pageY + 'px';
                    this.contextMenu.file = file;
                    this.contextMenu.file.path = this.browser.path + '/' + file.name;
                    this.contextMenu.newName = file.name;
                    this.contextMenu.isRenaming = false;
                },
                hideContextMenu() {
                    this.contextMenu.visible = false;
                },
                startRename() {
                    this.contextMenu.isRenaming = true;
                    this.hideContextMenu();
                    // 聚焦到输入框
                    this.$nextTick(() => {
                        const input = document.querySelector('.input-group-sm input');
                        if (input) {
                            input.focus();
                            input.select();
                        }
                    });
                },
                cancelRename() {
                    this.contextMenu.isRenaming = false;
                    this.contextMenu.file = null;
                },
                async confirmRename() {
                    if (!this.contextMenu.file || !this.contextMenu.newName) return;

                    try {
                        const response = await axios.post(`/api/storage/${this.browser.storage}/rename`, {
                            files: [{
                                path: this.contextMenu.file.path,
                                new_name: this.contextMenu.newName
                            }]
                        });

                        if (response.data.success) {
                            this.showToast('重命名成功', "success");
                            this.contextMenu.isRenaming = false;
                            this.contextMenu.file = null;
                            this.browseFiles(this.browser.path);
                        } else {
                            this.showToast(response.data.message, "danger");
                        }
                    } catch (error) {
                        console.error('重命名失败:', error);
                        this.showToast('重命名失败: ' + (error.response?.data?.message || error.message), "danger");
                    }
                },
                async deleteFile() {
                    if (!this.contextMenu.file) return;

                    if (!confirm(`确定要删除 ${this.contextMenu.file.name} 吗？`)) {
                        this.hideContextMenu();
                        return;
                    }

                    try {
                        const response = await axios.post(`/api/storage/${this.browser.storage}/delete`, {
                            path: this.contextMenu.file.path
                        });

                        if (response.data.success) {
                            this.showToast('删除成功', "success");
                            this.hideContextMenu();
                            this.browseFiles(this.browser.path);
                        } else {
                            this.showToast(response.data.message, "danger");
                        }
                    } catch (error) {
                        console.error('删除失败:', error);
                        this.showToast('删除失败: ' + (error.response?.data?.message || error.message), "danger");
                    }
                },
                handleGlobalClick(event) {
                    // 如果点击的不是右键菜单和重命名输入框
                    if (!event.target.closest('.context-menu')) {
                        this.hideContextMenu();
                        if (this.contextMenu.isRenaming) {
                            this.cancelRename();
                        }
                    }
                },
                // ==============================
                // 批量重命名
                // ==============================
                toggleRenameMode() {
                    this.batchRename.enabled = !this.batchRename.enabled;
                    if (this.batchRename.enabled) {
                        if (this.batchRename.mode == "magic") {
                            this.browseFiles(this.browser.path);
                        } else {
                            this.previewRename();
                        }
                    }
                },
                previewRename() {
                    switch (this.batchRename.mode) {
                        case 'regex':
                            this.batchRename.canApply = this.batchRename.config.regex.pattern && this.batchRename.config.regex.replace;
                            break;
                        case 'sequence':
                            this.batchRename.canApply = this.batchRename.config.sequence.prefix && this.batchRename.config.sequence.startNum > 0;
                            break;
                        case 'magic':
                            this.batchRename.canApply = this.browser.files.some(file => file.name_magic);
                            break;
                        default:
                            this.batchRename.canApply = false;
                    }
                    if (this.batchRename.canApply) {
                        this.browser.files.forEach((file, index) => {
                            file.new_name = this.getPreviewName(file, index);
                        });
                    }
                },
                getPreviewName(file, index) {
                    if (file.isdir) return '';
                    switch (this.batchRename.mode) {
                        case 'regex':
                            try {
                                if (this.batchRename.config.regex.pattern == "") return file.name;
                                const regex = new RegExp(this.batchRename.config.regex.pattern);
                                return file.name.replace(regex, this.batchRename.config.regex.replace);
                            } catch (e) {
                                return '正则表达式无效';
                            }
                        case 'sequence':
                            const num = this.batchRename.config.sequence.startNum + index;
                            return `${this.batchRename.config.sequence.prefix}${num.toString().padStart(2, '0')}${this.batchRename.config.sequence.suffix}`;
                        case 'magic':
                            return file.name_magic || file.name;
                        default:
                            return file.name;
                    }
                },
                async applyRename() {
                    if (!this.browser.storage) {
                        this.showToast('请先选择存储', "info");
                        return;
                    }

                    const files = this.browser.files
                        .filter(file => !file.isdir && file.new_name !== file.name)
                        .map(file => ({
                            path: this.browser.path + '/' + file.name,
                            new_name: file.new_name
                        }));

                    if (files.length === 0) {
                        this.showToast('没有需要重命名的文件', "info");
                        return;
                    }

                    this.batchRename.renaming = true;
                    try {
                        const response = await axios.post(`/api/storage/${this.browser.storage}/rename`, {
                            files: files
                        });

                        if (response.data.success) {
                            this.showToast('批量重命名成功', "success");
                            this.browseFiles(this.browser.path);
                        } else {
                            this.showToast(response.data.message, "danger");
                        }
                    } catch (error) {
                        console.error('批量重命名失败:', error);
                        this.showToast('批量重命名失败: ' + (error.response?.data?.message || error.message), "danger");
                    } finally {
                        this.batchRename.renaming = false;
                    }
                },
            },
            computed: {
                driverConfig() {
                    const driver = this.availableDrivers.find(d => d.type === this.editingStorage.driver);
                    return driver ? driver.config : {};
                },
                driverTips() {
                    const driver = this.availableDrivers.find(d => d.type === this.editingStorage.driver);
                    return driver ? driver.tips : { type: 'info', message: '' };
                }
            }
        }).mount('#app');
    </script>
</body>

</html>