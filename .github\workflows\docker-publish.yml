name: Docker Publish

permissions:
  contents: write
  packages: write

on:
  workflow_dispatch:
  push:
    branches:
      - main
    tags:
      - "v*"

env:
  IMAGE_NAME: "Cp0204/SmartStrm"

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ${{ env.IMAGE_NAME }}
            ghcr.io/${{ env.IMAGE_NAME }}
            registry.cn-shenzhen.aliyuncs.com/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=tag

      - name: Get version
        id: get_version
        run: echo ::set-output name=VERSION::${GITHUB_REF/refs\/tags\//}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Login to Aliyun
        uses: docker/login-action@v3
        with:
          registry: registry.cn-shenzhen.aliyuncs.com
          username: ${{ secrets.ALIYUN_USERNAME }}
          password: ${{ secrets.ALIYUN_TOKEN }}

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          build-args: |
            MAINTAINER=${{ github.repository_owner }}
            BRANCH=${{ github.ref_name }}
            BUILD_SHA=${{ github.sha }}
            BUILD_TAG=${{ steps.get_version.outputs.VERSION }}
          context: .
          platforms: |
            linux/amd64
            linux/arm64
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

      - name: Update repo description
        uses: peter-evans/dockerhub-description@v4
        if: startsWith(github.ref, 'refs/tags/')
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
          repository: ${{ env.IMAGE_NAME }}
          short-description: ${{ github.event.repository.description }}
          enable-url-completion: true

      - name: Get Commit Messages
        id: get_commit_messages
        run: |
          # Get the latest tag
          LATEST_TAG=$(git describe --tags --abbrev=0 HEAD^)

          # If there is no latest tag, get only the latest commit message
          if [ -z "$LATEST_TAG" ]; then
            COMMITS=$(git log -1 --pretty=format:'- %s (%h)')
          else
            COMMITS=$(git log --pretty=format:'- %s (%h)' --no-merges "$LATEST_TAG"..HEAD)
          fi

          # Output Multi-Line String
          echo "commits<<EOF" >> $GITHUB_OUTPUT
          echo "$COMMITS" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Upload New Release
        uses: softprops/action-gh-release@v2
        if: startsWith(github.ref, 'refs/tags/')
        with:
          draft: false
          prerelease: false
          body: "## What's Changed\n\n${{ steps.get_commit_messages.outputs.commits }}"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}